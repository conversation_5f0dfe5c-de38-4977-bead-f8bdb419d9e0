/// QR Code Page - GymKod Pro Mobile
///
/// Bu sayfa Angular frontend'deki my-qr component'inden uyarlanmıştır.
/// Referans: GymProjectFrontend/src/app/components/my-qr/my-qr.component.html
///
/// RESPONSIVE DESIGN:
/// - Responsive QR code sizing (mobile: 200px, tablet: 250px, desktop: 300px)
/// - Responsive layout spacing ve padding
/// - Responsive typography scaling
/// - Responsive container sizing ve positioning
/// - Responsive icon sizes ve button heights
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../core/core.dart';
import '../../../../core/providers/profile_image_provider.dart';
import '../../../../shared/widgets/responsive_builder.dart';
import '../../../auth/presentation/providers/auth_provider.dart';
import '../providers/qr_code_provider.dart';
import '../../data/services/qr_timer_service.dart';

/// QR Code Page
/// Angular frontend'deki my-qr component'e benzer
class QRCodePage extends ConsumerStatefulWidget {
  const QRCodePage({super.key});

  @override
  ConsumerState<QRCodePage> createState() => _QRCodePageState();
}

class _QRCodePageState extends ConsumerState<QRCodePage> {
  @override
  void initState() {
    super.initState();

    // Sayfa açıldığında QR kodunu yükle (Angular: ngOnInit)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final qrState = ref.read(qrCodeProvider);
      // QR kod yoksa veya süresi dolmuşsa yükle
      if (qrState.qrData == null || !qrState.isQRActive) {
        ref.read(qrCodeProvider.notifier).loadQRCode();
      }

      // Profil fotoğrafını yükle (timestamp korunacak)
      final currentImageUrl = ref.read(profileImageUrlProvider);
      LoggingService.info('QR Page Init - Current Image URL: $currentImageUrl', tag: 'QR_PROFILE');

      if (currentImageUrl == null) {
        LoggingService.info('QR Page Init - Loading profile image...', tag: 'QR_PROFILE');
        ref.read(profileImageProvider.notifier).loadProfileImage();
      } else {
        LoggingService.info('QR Page Init - Profile image already loaded', tag: 'QR_PROFILE');
      }
    });
  }

  /// QR sayfası için responsive profil fotoğrafı boyutu
  double _getQRProfileImageSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 600) {
      return 60; // Tablet/Desktop
    } else if (screenWidth > 400) {
      return 50; // Büyük telefon
    } else {
      return 44; // Küçük telefon
    }
  }

  /// iPhone SE ve küçük ekranlar için optimize edilmiş QR kod boyutu
  double _getOptimizedQRSize(double screenWidth, double screenHeight) {
    // iPhone SE ve çok küçük ekranlar için özel optimizasyon
    if (screenWidth <= 375 && screenHeight <= 667) {
      // iPhone SE (375x667) - Ekranın ~55%'i, daha kompakt
      return 200.0;
    } else if (screenWidth <= 375) {
      // Diğer 375px genişlikli ekranlar
      return 220.0;
    } else if (screenWidth <= 414) {
      // iPhone 6/7/8 Plus (414px) gibi orta boy ekranlar
      return 250.0;
    } else if (screenWidth < ResponsiveBreakpoints.xs) {
      // Diğer mobil cihazlar (576px'e kadar)
      return 270.0;
    } else if (screenWidth < ResponsiveBreakpoints.md) {
      // Tablet boyutu
      return 320.0;
    } else {
      // Desktop boyutu
      return 360.0;
    }
  }

  /// Küçük ekranlar için optimize edilmiş container padding
  double _getOptimizedContainerPadding(double screenWidth, double screenHeight) {
    // iPhone SE ve çok küçük ekranlar için özel optimizasyon
    if (screenWidth <= 375 && screenHeight <= 667) {
      // iPhone SE (375x667) - Minimum padding
      return 10.0;
    } else if (screenWidth <= 375) {
      // Diğer 375px genişlikli ekranlar
      return 12.0;
    } else if (screenWidth <= 414) {
      // Orta boy mobil cihazlar
      return 14.0;
    } else if (screenWidth < ResponsiveBreakpoints.xs) {
      // Büyük mobil cihazlar
      return 16.0;
    } else if (screenWidth < ResponsiveBreakpoints.md) {
      // Tablet
      return 20.0;
    } else {
      // Desktop
      return 24.0;
    }
  }

  /// Küçük ekranlar için optimize edilmiş border radius
  double _getOptimizedBorderRadius(double screenWidth) {
    if (screenWidth <= 375) {
      // iPhone SE ve daha küçük ekranlar - daha küçük radius
      return 12.0;
    } else if (screenWidth <= 414) {
      // Orta boy mobil cihazlar
      return 14.0;
    } else if (screenWidth < ResponsiveBreakpoints.xs) {
      // Büyük mobil cihazlar
      return 16.0;
    } else if (screenWidth < ResponsiveBreakpoints.md) {
      // Tablet
      return 20.0;
    } else {
      // Desktop
      return 24.0;
    }
  }

  /// Optimize edilmiş profil resmi widget'ı
  Widget _buildProfileImage(BuildContext context, ThemeData theme) {
    return Consumer(
      builder: (context, ref, child) {
        final profileImageUrl = ref.watch(profileImageUrlProvider);

        return Container(
          width: _getQRProfileImageSize(context),
          height: _getQRProfileImageSize(context),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface.withValues(alpha: 0.9),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: profileImageUrl != null
              ? ClipOval(
                  child: CachedNetworkImage(
                    imageUrl: profileImageUrl,
                    width: _getQRProfileImageSize(context),
                    height: _getQRProfileImageSize(context),
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Padding(
                      padding: EdgeInsets.all(AppSpacing.responsive(context,
                        mobile: 8.0,
                        tablet: 10.0,
                        desktop: 12.0,
                      )),
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    errorWidget: (context, url, error) => Padding(
                      padding: EdgeInsets.all(AppSpacing.responsive(context,
                        mobile: 8.0,
                        tablet: 10.0,
                        desktop: 12.0,
                      )),
                      child: Icon(
                        Icons.person,
                        size: AppSpacing.responsiveIconSize(context,
                          mobile: 20.0,
                          tablet: 24.0,
                          desktop: 28.0,
                        ),
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ),
                )
              : Padding(
                  padding: EdgeInsets.all(AppSpacing.responsive(context,
                    mobile: 8.0,
                    tablet: 10.0,
                    desktop: 12.0,
                  )),
                  child: Icon(
                    Icons.person,
                    size: AppSpacing.responsiveIconSize(context,
                      mobile: 20.0,
                      tablet: 24.0,
                      desktop: 28.0,
                    ),
                    color: theme.colorScheme.primary,
                  ),
                ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final qrState = ref.watch(qrCodeProvider);
    final timerState = ref.watch(qrTimerProvider);

    return ResponsiveBuilder(
      builder: (context, deviceType) {
        return Scaffold(

          // Responsive Body
          body: ResponsiveContainer(
            child: Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withValues(alpha: 0.8),
                    theme.colorScheme.surface,
                  ],
                ),
              ),
              child: SafeArea(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    return Column(
                      children: [
                        // Responsive Welcome Header
                        _buildWelcomeHeader(theme, deviceType),

                        // iPhone SE için optimize edilmiş header spacing
                        SizedBox(height: constraints.maxHeight < 600 ? 8.0 :
                          AppSpacing.responsive(context,
                            mobile: 16.0,
                            tablet: 20.0,
                            desktop: 24.0,
                          )
                        ),

                        // Responsive Content Section - iPhone SE için optimize edilmiş
                        Expanded(
                          child: Padding(
                            padding: AppSpacing.responsiveScreenPadding(context),
                            child: LayoutBuilder(
                              builder: (context, constraints) {
                                // iPhone SE ve küçük ekranlar için özel düzenleme
                                final screenHeight = MediaQuery.of(context).size.height;
                                final screenWidth = MediaQuery.of(context).size.width;
                                final isSmallScreen = screenHeight <= 667 || screenWidth <= 375; // iPhone SE: 667x375

                                return SingleChildScrollView(
                                  physics: isSmallScreen
                                    ? const ClampingScrollPhysics()
                                    : const NeverScrollableScrollPhysics(),
                                  child: ConstrainedBox(
                                    constraints: BoxConstraints(
                                      minHeight: isSmallScreen
                                        ? 0
                                        : constraints.maxHeight,
                                    ),
                                    child: IntrinsicHeight(
                                      child: Column(
                                        mainAxisAlignment: isSmallScreen
                                          ? MainAxisAlignment.start
                                          : MainAxisAlignment.center,
                                        children: [
                                          // iPhone SE için üst boşluk
                                          if (isSmallScreen)
                                            SizedBox(height: AppSpacing.responsive(context,
                                              mobile: 8.0,
                                              tablet: 12.0,
                                              desktop: 16.0,
                                            )),

                                          // Content Section
                                          if (qrState.isLoading)
                                            _buildResponsiveLoadingSection(theme, deviceType)
                                          else if (qrState.error != null)
                                            _buildResponsiveErrorSection(theme, qrState.error!, qrState, deviceType)
                                          else if (qrState.qrData != null)
                                            _buildResponsiveQRSection(theme, qrState, timerState, deviceType)
                                          else
                                            _buildResponsiveEmptySection(theme, deviceType),

                                          // iPhone SE için alt boşluk
                                          if (isSmallScreen)
                                            SizedBox(height: AppSpacing.responsive(context,
                                              mobile: 8.0,
                                              tablet: 12.0,
                                              desktop: 16.0,
                                            )),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),

                        // Responsive Footer Section
                        _buildResponsiveFooterSection(theme, deviceType),
                      ],
                    );
                  },
                ),
              ),
            ),
          ),
        );
      },
    );
  }



  /// Responsive Loading Section
  Widget _buildResponsiveLoadingSection(ThemeData theme, DeviceType deviceType) {
    return ResponsiveContainer(
      padding: AppSpacing.responsivePadding(context,
        mobile: const EdgeInsets.all(30.0),
        tablet: const EdgeInsets.all(40.0),
        desktop: const EdgeInsets.all(50.0),
      ),
      child: Column(
        children: [
          SizedBox(
            width: AppSpacing.responsive(context,
              mobile: 32.0,
              tablet: 40.0,
              desktop: 48.0,
            ),
            height: AppSpacing.responsive(context,
              mobile: 32.0,
              tablet: 40.0,
              desktop: 48.0,
            ),
            child: CircularProgressIndicator(
              color: theme.colorScheme.primary,
              strokeWidth: deviceType == DeviceType.mobile ? 3.0 : 4.0,
            ),
          ),
          ResponsiveSpacing.vertical(
            mobile: 12.0,
            tablet: 16.0,
            desktop: 20.0,
          ),
          ResponsiveText(
            'QR kod yükleniyor...',
            textType: 'bodylarge',
            style: TextStyle(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// Responsive Error Section
  Widget _buildResponsiveErrorSection(ThemeData theme, String error, qrState, DeviceType deviceType) {
    return ResponsiveCard(
      padding: AppSpacing.responsiveCardPadding(context),
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            size: AppSpacing.responsiveIconSize(context,
              mobile: 40.0,
              tablet: 48.0,
              desktop: 56.0,
            ),
            color: theme.colorScheme.error,
          ),
          ResponsiveSpacing.vertical(
            mobile: 12.0,
            tablet: 16.0,
            desktop: 20.0,
          ),
          ResponsiveText(
            error,
            textType: 'bodylarge',
            style: TextStyle(
              color: theme.colorScheme.onErrorContainer,
            ),
            textAlign: TextAlign.center,
          ),
          ResponsiveSpacing.vertical(
            mobile: 12.0,
            tablet: 16.0,
            desktop: 20.0,
          ),
          SizedBox(
            height: AppSpacing.responsiveButtonHeight(context),
            child: ElevatedButton(
              onPressed: qrState.canRefresh && !qrState.isLoading
                  ? () => ref.read(qrCodeProvider.notifier).loadQRCode()
                  : null,
              child: ResponsiveText(
                qrState.canRefresh
                    ? 'Tekrar Dene'
                    : 'Lütfen 3 dakika bekleyin',
                textType: 'bodymedium',
                style: const TextStyle(fontWeight: FontWeight.w600),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Responsive Empty Section
  Widget _buildResponsiveEmptySection(ThemeData theme, DeviceType deviceType) {
    return ResponsiveContainer(
      padding: AppSpacing.responsivePadding(context,
        mobile: const EdgeInsets.all(30.0),
        tablet: const EdgeInsets.all(40.0),
        desktop: const EdgeInsets.all(50.0),
      ),
      child: Column(
        children: [
          Icon(
            Icons.qr_code_2,
            size: AppSpacing.responsiveIconSize(context,
              mobile: 56.0,
              tablet: 64.0,
              desktop: 72.0,
            ),
            color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          ResponsiveSpacing.vertical(
            mobile: 12.0,
            tablet: 16.0,
            desktop: 20.0,
          ),
          ResponsiveText(
            'QR kod bulunamadı',
            textType: 'bodylarge',
            style: TextStyle(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// Responsive QR Section
  Widget _buildResponsiveQRSection(ThemeData theme, qrState, timerState, DeviceType deviceType) {
    final qrData = qrState.qrData!;

    // iPhone SE için özel spacing optimizasyonu
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenWidth <= 375 && screenHeight <= 667;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Responsive Member Info
        if (!qrData.isFrozen && qrState.canShowQR) ...[
          _buildResponsiveMemberInfo(theme, qrData, deviceType),
          isSmallScreen
            ? SizedBox(height: 12.0)
            : ResponsiveSpacing.vertical(
                mobile: 16.0,
                tablet: 20.0,
                desktop: 24.0,
              ),
        ],

        // Responsive QR Code Container
        if (qrState.canShowQR && !qrData.isFrozen)
          _buildResponsiveQRCodeContainer(theme, qrData, timerState, deviceType)
        else if (qrData.isFrozen)
          _buildResponsiveFrozenSection(theme, qrData, deviceType)
        else
          _buildResponsiveExpiredSection(theme, deviceType),
      ],
    );
  }

  /// Responsive Member Info
  Widget _buildResponsiveMemberInfo(ThemeData theme, qrData, DeviceType deviceType) {
    return const SizedBox.shrink(); // Boş widget döndür
  }





  /// Responsive QR Code Container
  Widget _buildResponsiveQRCodeContainer(ThemeData theme, qrData, timerState, DeviceType deviceType) {
    // iPhone SE ve küçük ekranlar için özel optimizasyon
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final qrSize = _getOptimizedQRSize(screenWidth, screenHeight);

    // Küçük ekranlar için padding'i optimize et
    final containerPadding = _getOptimizedContainerPadding(screenWidth, screenHeight);

    // Küçük ekranlar için border radius'u optimize et
    final borderRadius = _getOptimizedBorderRadius(screenWidth);

    final borderWidth = deviceType == DeviceType.mobile ? 2.0 : 3.0;
    final shadowBlur = AppSpacing.responsiveElevation(context) * 2;

    return Column(
      children: [
        // Responsive QR Code Container
        Container(
          padding: EdgeInsets.all(containerPadding),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(borderRadius),
            border: Border.all(
              color: theme.colorScheme.primary,
              width: borderWidth,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: deviceType == DeviceType.mobile ? 0.12 : 0.15),
                blurRadius: shadowBlur,
                offset: Offset(0, shadowBlur / 2),
              ),
            ],
          ),
          child: QrImageView(
            data: qrData.scanNumber,
            version: QrVersions.auto,
            size: qrSize,
            backgroundColor: Colors.white,
            errorCorrectionLevel: QrErrorCorrectLevel.M, // Angular: 'M'
            dataModuleStyle: const QrDataModuleStyle(
              dataModuleShape: QrDataModuleShape.square,
              color: Colors.black,
            ),
            eyeStyle: const QrEyeStyle(
              eyeShape: QrEyeShape.square,
              color: Colors.black,
            ),
          ),
        ),

        // iPhone SE için optimize edilmiş spacing
        SizedBox(height: screenWidth <= 375 && screenHeight <= 667 ? 6.0 :
          AppSpacing.responsive(context,
            mobile: 8.0,
            tablet: 12.0,
            desktop: 16.0,
          )
        ),

        // Responsive Timer Section
        _buildResponsiveTimerSection(theme, timerState, deviceType),
      ],
    );
  }

  /// Responsive Timer Section
  Widget _buildResponsiveTimerSection(ThemeData theme, timerState, DeviceType deviceType) {
    final isWarning = ref.watch(qrTimerIsWarningProvider);
    final isAutoRefreshSoon = timerState.remainingTime.inSeconds <= 10; // Son 10 saniye

    // iPhone SE için özel padding optimizasyonu
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenWidth <= 375 && screenHeight <= 667;

    return ResponsiveCard(
      padding: isSmallScreen
        ? const EdgeInsets.symmetric(horizontal: 12, vertical: 8)
        : AppSpacing.responsivePadding(context,
            mobile: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            tablet: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            desktop: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
          ),
      child: Column(
        children: [
          // Responsive Timer Display
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                isAutoRefreshSoon ? Icons.autorenew : Icons.access_time,
                size: AppSpacing.responsiveIconSize(context,
                  mobile: 18.0,
                  tablet: 20.0,
                  desktop: 22.0,
                ),
                color: isWarning ? theme.colorScheme.error : theme.colorScheme.primary,
              ),
              ResponsiveSpacing.horizontal(
                mobile: 6.0,
                tablet: 8.0,
                desktop: 10.0,
              ),
              ResponsiveText(
                timerState.formattedTime,
                textType: 'h4',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: isWarning ? theme.colorScheme.error : theme.colorScheme.primary,
                ),
              ),
            ],
          ),

          // Auto refresh warning
          if (isAutoRefreshSoon) ...[
            ResponsiveSpacing.vertical(
              mobile: 6.0,
              tablet: 8.0,
              desktop: 10.0,
            ),
            ResponsiveText(
              'Otomatik yenileniyor...',
              textType: 'bodysmall',
              style: TextStyle(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],

          ResponsiveSpacing.vertical(
            mobile: 10.0,
            tablet: 12.0,
            desktop: 14.0,
          ),

          // Responsive Progress Bar
          Container(
            height: AppSpacing.responsive(context,
              mobile: 6.0,
              tablet: 8.0,
              desktop: 10.0,
            ),
            decoration: BoxDecoration(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
                mobile: 3.0,
                tablet: 4.0,
                desktop: 5.0,
              )),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: timerState.progress,
              child: Container(
                decoration: BoxDecoration(
                  color: isWarning ? theme.colorScheme.error : theme.colorScheme.primary,
                  borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
                    mobile: 3.0,
                    tablet: 4.0,
                    desktop: 5.0,
                  )),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Responsive Frozen Section
  Widget _buildResponsiveFrozenSection(ThemeData theme, qrData, DeviceType deviceType) {
    return ResponsiveCard(
      padding: AppSpacing.responsiveCardPadding(context),
      child: Column(
        children: [
          Icon(
            Icons.ac_unit,
            size: AppSpacing.responsiveIconSize(context,
              mobile: 40.0,
              tablet: 48.0,
              desktop: 56.0,
            ),
            color: theme.colorScheme.error,
          ),
          ResponsiveSpacing.vertical(
            mobile: 12.0,
            tablet: 16.0,
            desktop: 20.0,
          ),
          ResponsiveText(
            'Üyeliğiniz Dondurulmuş',
            textType: 'h3',
            style: TextStyle(
              color: theme.colorScheme.onErrorContainer,
              fontWeight: FontWeight.bold,
            ),
          ),
          ResponsiveSpacing.vertical(
            mobile: 6.0,
            tablet: 8.0,
            desktop: 10.0,
          ),
          if (qrData.freezeEndDate != null)
            ResponsiveText(
              'Açılış Tarihi: ${qrData.freezeEndDate!.day}/${qrData.freezeEndDate!.month}/${qrData.freezeEndDate!.year}',
              textType: 'bodymedium',
              style: TextStyle(
                color: theme.colorScheme.onErrorContainer,
              ),
            ),
        ],
      ),
    );
  }

  /// Responsive Expired Section
  Widget _buildResponsiveExpiredSection(ThemeData theme, DeviceType deviceType) {
    return ResponsiveCard(
      padding: AppSpacing.responsiveCardPadding(context),
      child: Column(
        children: [
          // Responsive Loading animation
          SizedBox(
            width: AppSpacing.responsive(context,
              mobile: 32.0,
              tablet: 40.0,
              desktop: 48.0,
            ),
            height: AppSpacing.responsive(context,
              mobile: 32.0,
              tablet: 40.0,
              desktop: 48.0,
            ),
            child: CircularProgressIndicator(
              color: theme.colorScheme.primary,
              strokeWidth: deviceType == DeviceType.mobile ? 3.0 : 4.0,
            ),
          ),
          ResponsiveSpacing.vertical(
            mobile: 12.0,
            tablet: 16.0,
            desktop: 20.0,
          ),
          ResponsiveText(
            'QR Kod Yenileniyor...',
            textType: 'h3',
            style: TextStyle(
              color: theme.colorScheme.onPrimaryContainer,
              fontWeight: FontWeight.bold,
            ),
          ),
          ResponsiveSpacing.vertical(
            mobile: 6.0,
            tablet: 8.0,
            desktop: 10.0,
          ),
          ResponsiveText(
            'QR kodunuzun süresi doldu. Yeni QR kod otomatik olarak yükleniyor.',
            textType: 'bodymedium',
            style: TextStyle(
              color: theme.colorScheme.onPrimaryContainer,
            ),
            textAlign: TextAlign.center,
          ),
          ResponsiveSpacing.vertical(
            mobile: 12.0,
            tablet: 16.0,
            desktop: 20.0,
          ),
          SizedBox(
            height: AppSpacing.responsiveButtonHeight(context),
            child: ElevatedButton(
              onPressed: () => ref.read(qrCodeProvider.notifier).refreshQRCode(),
              child: ResponsiveText(
                'Manuel Yenile',
                textType: 'bodymedium',
                style: const TextStyle(fontWeight: FontWeight.w600),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Responsive Footer Section
  Widget _buildResponsiveFooterSection(ThemeData theme, DeviceType deviceType) {
    return ResponsiveContainer(
      padding: AppSpacing.responsivePadding(context,
        mobile: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
        tablet: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        desktop: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 16.0),
      ),
      child: Center(
        child: ResponsiveText(
          'GymKod Pro v${AppConstants.appVersion}',
          textType: 'bodysmall',
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.7),
          ),
        ),
      ),
    );
  }

  /// Welcome Header Section
  Widget _buildWelcomeHeader(ThemeData theme, DeviceType deviceType) {
    final user = ref.watch(currentUserProvider);
    final qrState = ref.watch(qrCodeProvider);

    return ResponsiveContainer(
      padding: AppSpacing.responsivePadding(context,
        mobile: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        tablet: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10.0),
        desktop: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0),
      ),
      child: Column(
        children: [
          // Merhaba + İsim
          Row(
            children: [
              // Avatar/Profile Image - Global Provider (Optimized)
              _buildProfileImage(context, theme),

              SizedBox(width: AppSpacing.responsive(context,
                mobile: 12.0,
                tablet: 16.0,
                desktop: 20.0,
              )),

              // Merhaba + İsim
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveText(
                      'Merhaba,',
                      textType: 'bodymedium',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    if (user != null) ...[
                      ResponsiveText(
                        user.name,
                        textType: 'h3',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),

              // Kalan Gün Bilgisi
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppSpacing.responsive(context,
                    mobile: 12.0,
                    tablet: 16.0,
                    desktop: 20.0,
                  ),
                  vertical: AppSpacing.responsive(context,
                    mobile: 8.0,
                    tablet: 10.0,
                    desktop: 12.0,
                  ),
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.white.withValues(alpha: 0.2),
                      Colors.white.withValues(alpha: 0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    ResponsiveText(
                      _extractRemainingDaysNumber(qrState.qrData?.remainingDays),
                      textType: 'h2',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    ResponsiveText(
                      'Kalan Gün',
                      textType: 'bodysmall',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.8),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: AppSpacing.responsive(context,
            mobile: 8.0,
            tablet: 10.0,
            desktop: 12.0,
          )),

          // QR Kod Açıklama
          Container(
            padding: EdgeInsets.all(AppSpacing.responsive(context,
              mobile: 12.0,
              tablet: 16.0,
              desktop: 20.0,
            )),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.qr_code,
                  size: AppSpacing.responsiveIconSize(context,
                    mobile: 18.0,
                    tablet: 20.0,
                    desktop: 22.0,
                  ),
                  color: Colors.white.withValues(alpha: 0.9),
                ),
                SizedBox(width: AppSpacing.responsive(context,
                  mobile: 8.0,
                  tablet: 10.0,
                  desktop: 12.0,
                )),
                Expanded(
                  child: ResponsiveText(
                    'QR kodunuzu girişte okutarak spor salonunuza giriş yapabilirsiniz',
                    textType: 'bodysmall',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.9),
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Kalan gün string'inden sadece sayıyı çıkar
  String _extractRemainingDaysNumber(String? remainingDays) {
    if (remainingDays == null || remainingDays.isEmpty) return '0';

    // "Fitness: 29 Gün" formatından sadece sayıyı çıkar
    final regex = RegExp(r'(\d+)');
    final match = regex.firstMatch(remainingDays);

    if (match != null) {
      return match.group(1) ?? '0';
    }

    // Eğer sadece sayı varsa direkt döndür
    if (int.tryParse(remainingDays) != null) {
      return remainingDays;
    }

    return '0';
  }
}
